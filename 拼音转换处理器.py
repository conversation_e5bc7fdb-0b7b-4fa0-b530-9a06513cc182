import pandas as pd
from pypinyin import lazy_pinyin, Style
import re
import os

def chinese_to_pinyin_city_name(chinese_name):
    """
    将中文城市名转换为拼音形式的英文名
    """
    if not chinese_name or not isinstance(chinese_name, str):
        return chinese_name
    
    # 如果已经是英文，直接返回
    if re.match(r'^[A-Za-z\s]+$', chinese_name):
        return chinese_name
    
    # 转换为拼音
    pinyin_list = lazy_pinyin(chinese_name, style=Style.NORMAL)
    
    # 将拼音首字母大写，其余小写，然后连接
    english_name = ''.join([word.capitalize() for word in pinyin_list])
    
    return english_name

def process_excel_with_pinyin(input_file):
    """
    使用拼音转换处理Excel文件
    """
    try:
        print(f"正在读取文件: {input_file}")
        df = pd.read_excel(input_file)
        
        print(f"数据形状: {df.shape}")
        print("前5行数据:")
        print(df.head())
        
        if 'Target' not in df.columns:
            print("错误: 未找到Target列")
            return None
        
        print(f"\n转换前Target列唯一值数量: {df['Target'].nunique()}")
        print("转换前部分唯一值:")
        unique_before = df['Target'].unique()
        for i, city in enumerate(unique_before[:10]):
            print(f"  {city}")
        if len(unique_before) > 10:
            print(f"  ... 还有 {len(unique_before) - 10} 个城市")
        
        # 执行转换
        print("\n正在转换城市名...")
        df['Target'] = df['Target'].apply(chinese_to_pinyin_city_name)
        
        print(f"\n转换后Target列唯一值数量: {df['Target'].nunique()}")
        print("转换后部分唯一值:")
        unique_after = df['Target'].unique()
        for i, city in enumerate(unique_after[:10]):
            print(f"  {city}")
        if len(unique_after) > 10:
            print(f"  ... 还有 {len(unique_after) - 10} 个城市")
        
        # 生成输出文件名
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_拼音转换.xlsx"
        
        # 保存结果
        print(f"\n正在保存到: {output_file}")
        df.to_excel(output_file, index=False)
        print("保存成功!")
        
        # 同时保存CSV版本
        csv_output = f"{base_name}_拼音转换.csv"
        df.to_csv(csv_output, index=False, encoding='utf-8-sig')
        print(f"同时保存CSV版本到: {csv_output}")
        
        return df
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def show_conversion_examples():
    """
    显示一些转换示例
    """
    print("拼音转换示例:")
    print("="*40)
    
    examples = [
        '安庆', '淮南', '淮北', '阜阳', '亳州', '蚌埠',
        '宜宾', '扬州', '盐城', '徐州', '泰州', '宿州',
        '上海', '北京', '广州', '深圳', '杭州', '南京'
    ]
    
    for city in examples:
        english = chinese_to_pinyin_city_name(city)
        print(f"{city:>4} -> {english}")

if __name__ == "__main__":
    print("中文城市名拼音转换处理器")
    print("="*50)
    
    # 显示转换示例
    show_conversion_examples()
    
    print("\n" + "="*50)
    
    # 处理Excel文件
    input_file = "长江经济带筛选(1).xlsx"
    if os.path.exists(input_file):
        result = process_excel_with_pinyin(input_file)
        if result is not None:
            print(f"\n处理完成! 共转换了 {len(result)} 行数据")
        else:
            print("\n处理失败!")
    else:
        print(f"文件 {input_file} 不存在")
        print("请确保文件在当前目录中")
