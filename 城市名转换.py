import pandas as pd

def convert_chinese_cities_to_english():
    """
    将表格中Target列的中文城市名转换为英文
    """
    
    # 中文城市名到英文的映射字典
    city_mapping = {
        # 长江经济带主要城市
        '上海': 'Shanghai',
        '重庆': 'Chongqing',
        '南京': 'Nanjing',
        '无锡': 'Wuxi',
        '苏州': 'Suzhou',
        '南通': 'Nantong',
        '盐城': 'Yancheng',
        '扬州': 'Yangzhou',
        '镇江': 'Zhenjiang',
        '泰州': 'Taizhou',
        '宿迁': 'Suqian',
        '杭州': 'Hangzhou',
        '宁波': 'Ningbo',
        '温州': 'Wenzhou',
        '嘉兴': 'Jiaxing',
        '湖州': 'Huzhou',
        '绍兴': 'Shaoxing',
        '金华': 'Jinhua',
        '衢州': 'Quzhou',
        '舟山': 'Zhoushan',
        '台州': 'Taizhou',
        '丽水': 'Lishui',
        '合肥': 'He<PERSON><PERSON>',
        '芜湖': '<PERSON>hu',
        '马鞍山': '<PERSON>anshan',
        '铜陵': 'Tongling',
        '安庆': 'Anqing',
        '滁州': 'Chuzhou',
        '池州': 'Chizhou',
        '宣城': 'Xuancheng',
        '南昌': 'Nanchang',
        '景德镇': 'Jingdezhen',
        '九江': 'Jiujiang',
        '新余': 'Xinyu',
        '鹰潭': 'Yingtan',
        '赣州': 'Ganzhou',
        '吉安': 'Jian',
        '宜春': 'Yichun',
        '抚州': 'Fuzhou',
        '上饶': 'Shangrao',
        '武汉': 'Wuhan',
        '黄石': 'Huangshi',
        '十堰': 'Shiyan',
        '宜昌': 'Yichang',
        '襄阳': 'Xiangyang',
        '鄂州': 'Ezhou',
        '荆门': 'Jingmen',
        '孝感': 'Xiaogan',
        '荆州': 'Jingzhou',
        '黄冈': 'Huanggang',
        '咸宁': 'Xianning',
        '随州': 'Suizhou',
        '恩施': 'Enshi',
        '仙桃': 'Xiantao',
        '潜江': 'Qianjiang',
        '天门': 'Tianmen',
        '神农架': 'Shennongjia',
        '长沙': 'Changsha',
        '株洲': 'Zhuzhou',
        '湘潭': 'Xiangtan',
        '衡阳': 'Hengyang',
        '邵阳': 'Shaoyang',
        '岳阳': 'Yueyang',
        '常德': 'Changde',
        '张家界': 'Zhangjiajie',
        '益阳': 'Yiyang',
        '郴州': 'Chenzhou',
        '永州': 'Yongzhou',
        '怀化': 'Huaihua',
        '娄底': 'Loudi',
        '湘西': 'Xiangxi',
        '成都': 'Chengdu',
        '自贡': 'Zigong',
        '攀枝花': 'Panzhihua',
        '泸州': 'Luzhou',
        '德阳': 'Deyang',
        '绵阳': 'Mianyang',
        '广元': 'Guangyuan',
        '遂宁': 'Suining',
        '内江': 'Neijiang',
        '乐山': 'Leshan',
        '南充': 'Nanchong',
        '眉山': 'Meishan',
        '宜宾': 'Yibin',
        '广安': 'Guangan',
        '达州': 'Dazhou',
        '雅安': 'Yaan',
        '巴中': 'Bazhong',
        '资阳': 'Ziyang',
        '阿坝': 'Aba',
        '甘孜': 'Ganzi',
        '凉山': 'Liangshan',
        '贵阳': 'Guiyang',
        '六盘水': 'Liupanshui',
        '遵义': 'Zunyi',
        '安顺': 'Anshun',
        '毕节': 'Bijie',
        '铜仁': 'Tongren',
        '黔西南': 'Qianxinan',
        '黔东南': 'Qiandongnan',
        '黔南': 'Qiannan',
        '昆明': 'Kunming',
        '曲靖': 'Qujing',
        '玉溪': 'Yuxi',
        '保山': 'Baoshan',
        '昭通': 'Zhaotong',
        '丽江': 'Lijiang',
        '普洱': 'Puer',
        '临沧': 'Lincang',
        '楚雄': 'Chuxiong',
        '红河': 'Honghe',
        '文山': 'Wenshan',
        '西双版纳': 'Xishuangbanna',
        '大理': 'Dali',
        '德宏': 'Dehong',
        '怒江': 'Nujiang',
        '迪庆': 'Diqing',
        # 其他常见城市
        '北京': 'Beijing',
        '天津': 'Tianjin',
        '石家庄': 'Shijiazhuang',
        '太原': 'Taiyuan',
        '呼和浩特': 'Hohhot',
        '沈阳': 'Shenyang',
        '长春': 'Changchun',
        '哈尔滨': 'Harbin',
        '福州': 'Fuzhou',
        '济南': 'Jinan',
        '郑州': 'Zhengzhou',
        '广州': 'Guangzhou',
        '南宁': 'Nanning',
        '海口': 'Haikou',
        '西安': 'Xian',
        '兰州': 'Lanzhou',
        '西宁': 'Xining',
        '银川': 'Yinchuan',
        '乌鲁木齐': 'Urumqi',
        '拉萨': 'Lhasa',
        '徐州': 'Xuzhou',
        '常州': 'Changzhou',
        '连云港': 'Lianyungang',
        '淮安': 'Huaian'
    }
    
    # 示例数据（基于您提供的表格内容）
    sample_data = {
        'Source': ['遵义', '遵义', '遵义', '遵义', '遵义'],
        'Target': ['宜宾', '扬州', '盐城', '徐州', '泰州'],
        'Type': ['Directed', 'Directed', 'Directed', 'Directed', 'Directed'],
        'Weight': [4, 4, 3, 2, 3]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(sample_data)
    
    print("原始数据:")
    print(df)
    print("\n" + "="*50 + "\n")
    
    # 转换Target列的中文城市名为英文
    df['Target'] = df['Target'].map(city_mapping).fillna(df['Target'])
    
    print("转换后的数据:")
    print(df)
    
    # 保存到CSV文件
    df.to_csv('转换后的城市数据.csv', index=False, encoding='utf-8-sig')
    print(f"\n数据已保存到 '转换后的城市数据.csv'")
    
    return df

def process_excel_file(file_path):
    """
    处理Excel文件中的城市名转换
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        print(f"从 {file_path} 读取数据:")
        print(df.head())
        print(f"数据形状: {df.shape}")
        
        # 中文城市名到英文的映射字典（同上）
        city_mapping = {
            '上海': 'Shanghai', '重庆': 'Chongqing', '南京': 'Nanjing', '无锡': 'Wuxi',
            '苏州': 'Suzhou', '南通': 'Nantong', '盐城': 'Yancheng', '扬州': 'Yangzhou',
            '镇江': 'Zhenjiang', '泰州': 'Taizhou', '宿迁': 'Suqian', '杭州': 'Hangzhou',
            '宁波': 'Ningbo', '温州': 'Wenzhou', '嘉兴': 'Jiaxing', '湖州': 'Huzhou',
            '绍兴': 'Shaoxing', '金华': 'Jinhua', '衢州': 'Quzhou', '舟山': 'Zhoushan',
            '台州': 'Taizhou', '丽水': 'Lishui', '合肥': 'Hefei', '芜湖': 'Wuhu',
            '马鞍山': 'Maanshan', '铜陵': 'Tongling', '安庆': 'Anqing', '滁州': 'Chuzhou',
            '池州': 'Chizhou', '宣城': 'Xuancheng', '南昌': 'Nanchang', '景德镇': 'Jingdezhen',
            '九江': 'Jiujiang', '新余': 'Xinyu', '鹰潭': 'Yingtan', '赣州': 'Ganzhou',
            '吉安': 'Jian', '宜春': 'Yichun', '抚州': 'Fuzhou', '上饶': 'Shangrao',
            '武汉': 'Wuhan', '黄石': 'Huangshi', '十堰': 'Shiyan', '宜昌': 'Yichang',
            '襄阳': 'Xiangyang', '鄂州': 'Ezhou', '荆门': 'Jingmen', '孝感': 'Xiaogan',
            '荆州': 'Jingzhou', '黄冈': 'Huanggang', '咸宁': 'Xianning', '随州': 'Suizhou',
            '恩施': 'Enshi', '仙桃': 'Xiantao', '潜江': 'Qianjiang', '天门': 'Tianmen',
            '神农架': 'Shennongjia', '长沙': 'Changsha', '株洲': 'Zhuzhou', '湘潭': 'Xiangtan',
            '衡阳': 'Hengyang', '邵阳': 'Shaoyang', '岳阳': 'Yueyang', '常德': 'Changde',
            '张家界': 'Zhangjiajie', '益阳': 'Yiyang', '郴州': 'Chenzhou', '永州': 'Yongzhou',
            '怀化': 'Huaihua', '娄底': 'Loudi', '湘西': 'Xiangxi', '成都': 'Chengdu',
            '自贡': 'Zigong', '攀枝花': 'Panzhihua', '泸州': 'Luzhou', '德阳': 'Deyang',
            '绵阳': 'Mianyang', '广元': 'Guangyuan', '遂宁': 'Suining', '内江': 'Neijiang',
            '乐山': 'Leshan', '南充': 'Nanchong', '眉山': 'Meishan', '宜宾': 'Yibin',
            '广安': 'Guangan', '达州': 'Dazhou', '雅安': 'Yaan', '巴中': 'Bazhong',
            '资阳': 'Ziyang', '阿坝': 'Aba', '甘孜': 'Ganzi', '凉山': 'Liangshan',
            '贵阳': 'Guiyang', '六盘水': 'Liupanshui', '遵义': 'Zunyi', '安顺': 'Anshun',
            '毕节': 'Bijie', '铜仁': 'Tongren', '黔西南': 'Qianxinan', '黔东南': 'Qiandongnan',
            '黔南': 'Qiannan', '昆明': 'Kunming', '曲靖': 'Qujing', '玉溪': 'Yuxi',
            '保山': 'Baoshan', '昭通': 'Zhaotong', '丽江': 'Lijiang', '普洱': 'Puer',
            '临沧': 'Lincang', '楚雄': 'Chuxiong', '红河': 'Honghe', '文山': 'Wenshan',
            '西双版纳': 'Xishuangbanna', '大理': 'Dali', '德宏': 'Dehong', '怒江': 'Nujiang',
            '迪庆': 'Diqing', '北京': 'Beijing', '天津': 'Tianjin', '石家庄': 'Shijiazhuang',
            '太原': 'Taiyuan', '呼和浩特': 'Hohhot', '沈阳': 'Shenyang', '长春': 'Changchun',
            '哈尔滨': 'Harbin', '福州': 'Fuzhou', '济南': 'Jinan', '郑州': 'Zhengzhou',
            '广州': 'Guangzhou', '南宁': 'Nanning', '海口': 'Haikou', '西安': 'Xian',
            '兰州': 'Lanzhou', '西宁': 'Xining', '银川': 'Yinchuan', '乌鲁木齐': 'Urumqi',
            '拉萨': 'Lhasa', '徐州': 'Xuzhou', '常州': 'Changzhou', '连云港': 'Lianyungang',
            '淮安': 'Huaian'
        }
        
        # 如果存在Target列，进行转换
        if 'Target' in df.columns:
            print(f"\n转换前Target列的唯一值:")
            print(df['Target'].unique())
            
            # 转换Target列
            df['Target'] = df['Target'].map(city_mapping).fillna(df['Target'])
            
            print(f"\n转换后Target列的唯一值:")
            print(df['Target'].unique())
            
            # 保存转换后的数据
            output_file = file_path.replace('.xlsx', '_转换后.xlsx')
            df.to_excel(output_file, index=False)
            print(f"\n转换后的数据已保存到: {output_file}")
            
            return df
        else:
            print("未找到Target列，请检查文件格式")
            return None
            
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

if __name__ == "__main__":
    print("城市名转换程序")
    print("="*50)
    
    # 方式1: 使用示例数据
    print("1. 使用示例数据进行转换:")
    convert_chinese_cities_to_english()
    
    print("\n" + "="*50 + "\n")
    
    # 方式2: 处理Excel文件
    excel_file = "长江经济带筛选(1).xlsx"
    print(f"2. 处理Excel文件: {excel_file}")
    process_excel_file(excel_file)
